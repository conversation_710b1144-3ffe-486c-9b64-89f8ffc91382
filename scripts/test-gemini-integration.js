#!/usr/bin/env node

/**
 * Test script to verify Gemini integration
 * Run with: node scripts/test-gemini-integration.js
 */

const { config } = require('dotenv');
const path = require('path');

// Load environment variables
config({ path: path.join(__dirname, '../.env') });

async function testGeminiIntegration() {
  console.log('🧪 Testing Gemini Integration...\n');

  // Check environment variables
  console.log('📋 Environment Configuration:');
  console.log(`  OpenAI API Key: ${process.env.LLM_OPENAI_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`  Gemini API Key: ${process.env.LLM_GEMINI_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`  Default Provider: ${process.env.LLM_DEFAULT_PROVIDER || 'openai'}`);
  console.log(`  OpenAI Model: ${process.env.LLM_OPENAI_MODEL || 'gpt-4o-mini'}`);
  console.log(`  Gemini Model: ${process.env.LLM_GEMINI_MODEL || 'gemini-1.5-flash'}\n`);

  // Test configuration loading
  try {
    console.log('⚙️  Testing configuration loading...');
    const { getLLMConfig } = require('../src/config/server.config.ts');
    const config = await getLLMConfig();
    
    console.log('✅ Configuration loaded successfully');
    console.log(`   Default Provider: ${config.defaultProvider}`);
    console.log(`   OpenAI Model: ${config.openAIModel}`);
    console.log(`   Gemini Model: ${config.geminiModel}\n`);
  } catch (error) {
    console.error('❌ Configuration loading failed:', error.message);
    return;
  }

  // Test Gemini service initialization
  try {
    console.log('🤖 Testing Gemini service initialization...');
    
    if (!process.env.LLM_GEMINI_API_KEY) {
      console.log('⚠️  Gemini API key not set, skipping Gemini tests');
    } else {
      const { GeminiService } = require('../src/backend/services/gemini.service.ts');
      const geminiService = new GeminiService();
      
      console.log('✅ Gemini service initialized');
      
      // Test connection
      console.log('🔗 Testing Gemini connection...');
      const connectionTest = await geminiService.testConnection();
      
      if (connectionTest) {
        console.log('✅ Gemini connection successful');
      } else {
        console.log('❌ Gemini connection failed');
      }
    }
  } catch (error) {
    console.error('❌ Gemini service test failed:', error.message);
  }

  // Test LLM service with both providers
  try {
    console.log('\n🔄 Testing LLM service provider selection...');
    
    // This would require more complex setup due to dependencies
    console.log('⚠️  Full LLM service test requires database and cache setup');
    console.log('   Run integration tests instead: yarn test llm-provider-integration.test.ts');
  } catch (error) {
    console.error('❌ LLM service test failed:', error.message);
  }

  console.log('\n✨ Integration test completed!');
  console.log('\n📚 Next steps:');
  console.log('   1. Set up your Gemini API key if not already done');
  console.log('   2. Run the full test suite: yarn test');
  console.log('   3. Start the development server: yarn dev');
  console.log('   4. Check the documentation: docs/GEMINI_INTEGRATION.md');
}

// Run the test
testGeminiIntegration().catch(console.error);
