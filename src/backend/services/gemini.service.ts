import { gemini15Flash, gemini15Pro, geminiPro } from '@genkit-ai/googleai';
import { generate } from '@genkit-ai/ai/generate';
import { getLLMConfig } from '@/config';
import { initializeGenkit, isGenkitInitialized } from '@/backend/config/genkit.config';
import { z } from 'zod';

export interface GeminiResponse {
	content: string;
	usage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};
}

export interface GeminiCompletionParams {
	model?: string;
	messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>;
	temperature?: number;
	maxTokens?: number;
	responseFormat?: any; // For structured output
}

export class GeminiService {
	private initialized = false;
	private initPromise: Promise<void> | null = null;

	constructor() {
		this.initPromise = this.initializeGenkit();
	}

	private async initializeGenkit(): Promise<void> {
		// Use centralized Genkit initialization
		await initializeGenkit();
		this.initialized = true;
	}

	private async ensureInitialized(): Promise<void> {
		if (this.initPromise) {
			await this.initPromise;
			this.initPromise = null;
		}
		if (!this.initialized) {
			throw new Error('Genkit service not initialized');
		}
	}

	/**
	 * Create a chat completion using Gemini
	 */
	async createChatCompletion(params: GeminiCompletionParams): Promise<GeminiResponse> {
		await this.ensureInitialized();
		const llmConfig = await getLLMConfig();

		// Select the appropriate model
		const modelName = params.model || llmConfig.geminiModel;
		let model;

		switch (modelName) {
			case 'gemini-1.5-flash':
				model = gemini15Flash;
				break;
			case 'gemini-1.5-pro':
				model = gemini15Pro;
				break;
			case 'gemini-pro':
				model = geminiPro;
				break;
			default:
				model = gemini15Flash; // Default fallback
		}

		// Convert OpenAI-style messages to a single prompt
		const prompt = this.convertMessagesToPrompt(params.messages);

		try {
			const result = await generate({
				model,
				prompt,
				config: {
					temperature: params.temperature ?? 0.7,
					maxOutputTokens: params.maxTokens ?? 1000,
				},
			});

			const text = result.text();

			// Handle structured output if responseFormat is specified
			let finalContent = text;
			if (params.responseFormat) {
				try {
					// Try to parse as JSON if structured output is requested
					const parsed = JSON.parse(text);
					finalContent = JSON.stringify(parsed);
				} catch {
					// If parsing fails, return as-is
					finalContent = text;
				}
			}

			return {
				content: finalContent,
				usage: {
					promptTokens: result.usage?.inputTokens || 0,
					completionTokens: result.usage?.outputTokens || 0,
					totalTokens:
						(result.usage?.inputTokens || 0) + (result.usage?.outputTokens || 0),
				},
			};
		} catch (error) {
			console.error('Genkit API error:', error);
			throw new Error(
				`Genkit API call failed: ${
					error instanceof Error ? error.message : 'Unknown error'
				}`
			);
		}
	}

	/**
	 * Convert OpenAI-style messages to a single prompt for Gemini
	 */
	private convertMessagesToPrompt(messages: Array<{ role: string; content: string }>): string {
		let prompt = '';

		for (const message of messages) {
			switch (message.role) {
				case 'system':
					prompt += `System: ${message.content}\n\n`;
					break;
				case 'user':
					prompt += `User: ${message.content}\n\n`;
					break;
				case 'assistant':
					prompt += `Assistant: ${message.content}\n\n`;
					break;
				default:
					prompt += `${message.content}\n\n`;
			}
		}

		return prompt.trim();
	}

	/**
	 * Create a completion with structured output using Zod schema
	 */
	async createStructuredCompletion<T>(
		params: GeminiCompletionParams,
		schema: z.ZodSchema<T>
	): Promise<{ data: T; usage?: GeminiResponse['usage'] }> {
		// Add JSON format instruction to the prompt
		const enhancedMessages = [...params.messages];
		const lastMessage = enhancedMessages[enhancedMessages.length - 1];

		if (lastMessage && lastMessage.role === 'system') {
			lastMessage.content +=
				'\n\nPlease respond with valid JSON that matches the required schema.';
		} else {
			enhancedMessages.push({
				role: 'system',
				content: 'Please respond with valid JSON that matches the required schema.',
			});
		}

		const response = await this.createChatCompletion({
			...params,
			messages: enhancedMessages,
			responseFormat: { type: 'json_object' },
		});

		try {
			const parsed = JSON.parse(response.content);
			const validated = schema.parse(parsed);

			return {
				data: validated,
				usage: response.usage,
			};
		} catch (error) {
			console.error('Failed to parse or validate Genkit response:', error);
			console.error('Raw response:', response.content);
			throw new Error(
				`Invalid response format from Genkit: ${
					error instanceof Error ? error.message : 'Unknown error'
				}`
			);
		}
	}

	/**
	 * Test the Genkit connection
	 */
	async testConnection(): Promise<boolean> {
		try {
			const response = await this.createChatCompletion({
				messages: [{ role: 'user', content: 'Hello, please respond with "OK"' }],
				temperature: 0,
				maxTokens: 10,
			});

			return response.content.toLowerCase().includes('ok');
		} catch (error) {
			console.error('Genkit connection test failed:', error);
			return false;
		}
	}
}
