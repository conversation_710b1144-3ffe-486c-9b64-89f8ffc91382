import { GoogleGenerativeA<PERSON>, GenerativeModel, GenerationConfig } from '@google/generative-ai';
import { getLLMConfig } from '@/config';
import { z } from 'zod';

export interface GeminiResponse {
	content: string;
	usage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};
}

export interface GeminiCompletionParams {
	model?: string;
	messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>;
	temperature?: number;
	maxTokens?: number;
	responseFormat?: any; // For structured output
}

export class GeminiService {
	private genAI: GoogleGenerativeAI | null = null;
	private initPromise: Promise<void> | null = null;

	constructor() {
		this.initPromise = this.initializeGemini();
	}

	private async initializeGemini(): Promise<void> {
		const llmConfig = await getLLMConfig();
		if (!llmConfig.geminiKey) {
			throw new Error('Gemini API key not configured');
		}
		this.genAI = new GoogleGenerativeAI(llmConfig.geminiKey);
	}

	private async ensureInitialized(): Promise<GoogleGenerativeAI> {
		if (this.initPromise) {
			await this.initPromise;
			this.initPromise = null;
		}
		if (!this.genAI) {
			throw new Error('Gemini service not initialized');
		}
		return this.genAI;
	}

	/**
	 * Create a chat completion using Gemini
	 */
	async createChatCompletion(params: GeminiCompletionParams): Promise<GeminiResponse> {
		const genAI = await this.ensureInitialized();
		const llmConfig = await getLLMConfig();
		
		const model = genAI.getGenerativeModel({ 
			model: params.model || llmConfig.geminiModel 
		});

		// Convert OpenAI-style messages to Gemini format
		const prompt = this.convertMessagesToPrompt(params.messages);

		// Configure generation settings
		const generationConfig: GenerationConfig = {
			temperature: params.temperature ?? 0.7,
			maxOutputTokens: params.maxTokens ?? 1000,
		};

		try {
			const result = await model.generateContent({
				contents: [{ role: 'user', parts: [{ text: prompt }] }],
				generationConfig,
			});

			const response = result.response;
			const text = response.text();

			// Handle structured output if responseFormat is specified
			let finalContent = text;
			if (params.responseFormat) {
				try {
					// Try to parse as JSON if structured output is requested
					const parsed = JSON.parse(text);
					finalContent = JSON.stringify(parsed);
				} catch (e) {
					// If parsing fails, return as-is
					finalContent = text;
				}
			}

			return {
				content: finalContent,
				usage: {
					promptTokens: result.response.usageMetadata?.promptTokenCount || 0,
					completionTokens: result.response.usageMetadata?.candidatesTokenCount || 0,
					totalTokens: result.response.usageMetadata?.totalTokenCount || 0,
				},
			};
		} catch (error) {
			console.error('Gemini API error:', error);
			throw new Error(`Gemini API call failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	/**
	 * Convert OpenAI-style messages to a single prompt for Gemini
	 */
	private convertMessagesToPrompt(messages: Array<{ role: string; content: string }>): string {
		let prompt = '';
		
		for (const message of messages) {
			switch (message.role) {
				case 'system':
					prompt += `System: ${message.content}\n\n`;
					break;
				case 'user':
					prompt += `User: ${message.content}\n\n`;
					break;
				case 'assistant':
					prompt += `Assistant: ${message.content}\n\n`;
					break;
				default:
					prompt += `${message.content}\n\n`;
			}
		}

		return prompt.trim();
	}

	/**
	 * Create a completion with structured output using Zod schema
	 */
	async createStructuredCompletion<T>(
		params: GeminiCompletionParams,
		schema: z.ZodSchema<T>
	): Promise<{ data: T; usage?: GeminiResponse['usage'] }> {
		// Add JSON format instruction to the prompt
		const enhancedMessages = [...params.messages];
		const lastMessage = enhancedMessages[enhancedMessages.length - 1];
		
		if (lastMessage && lastMessage.role === 'system') {
			lastMessage.content += '\n\nPlease respond with valid JSON that matches the required schema.';
		} else {
			enhancedMessages.push({
				role: 'system',
				content: 'Please respond with valid JSON that matches the required schema.',
			});
		}

		const response = await this.createChatCompletion({
			...params,
			messages: enhancedMessages,
			responseFormat: { type: 'json_object' },
		});

		try {
			const parsed = JSON.parse(response.content);
			const validated = schema.parse(parsed);
			
			return {
				data: validated,
				usage: response.usage,
			};
		} catch (error) {
			console.error('Failed to parse or validate Gemini response:', error);
			console.error('Raw response:', response.content);
			throw new Error(`Invalid response format from Gemini: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	/**
	 * Test the Gemini connection
	 */
	async testConnection(): Promise<boolean> {
		try {
			const response = await this.createChatCompletion({
				messages: [{ role: 'user', content: 'Hello, please respond with "OK"' }],
				temperature: 0,
				maxTokens: 10,
			});
			
			return response.content.toLowerCase().includes('ok');
		} catch (error) {
			console.error('Gemini connection test failed:', error);
			return false;
		}
	}
}
