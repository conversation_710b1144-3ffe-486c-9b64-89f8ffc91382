# Gemini Integration Guide

This document explains how to use Google's Gemini AI models alongside OpenAI in the Vocab application.

## Overview

The Vocab application now supports both OpenAI and Google Gemini as LLM providers. The system automatically selects the appropriate provider based on configuration and model requirements.

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Gemini Configuration
LLM_GEMINI_API_KEY=your_gemini_api_key_here
LLM_GEMINI_MODEL=gemini-1.5-flash
LLM_DEFAULT_PROVIDER=openai  # or 'gemini'

# Existing OpenAI Configuration
LLM_OPENAI_API_KEY=your_openai_api_key_here
LLM_OPENAI_MODEL=gpt-4o-mini
```

### 2. Getting Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key to your environment variables

## Available Models

### Gemini Models

- **gemini-1.5-flash**: Fast, cost-effective model for most tasks
- **gemini-1.5-pro**: High-quality model for complex tasks
- **gemini-pro**: Standard model for general use

### Model Selection Logic

The system automatically selects the provider based on:

1. **Model prefix**: If a model name starts with `gemini`, Gemini will be used
2. **Default provider**: Set via `LLM_DEFAULT_PROVIDER` environment variable
3. **Availability**: Falls back to available provider if the preferred one is not configured

## Usage

### Automatic Provider Selection

The LLMService automatically handles provider selection. No code changes are required for existing functionality.

```typescript
// This will use the configured default provider
const result = await llmService.generateRandomTerms({
  target_language: Language.EN,
  source_language: Language.VI,
  keywords: ['technology'],
  excludes: [],
  exclude_collections: [],
  userId: 'user123',
  count: 10,
});
```

### Force Specific Provider

You can force a specific provider by using model names:

```typescript
// Force Gemini usage
const geminiResult = await llmService.optimizedLLMCall(
  'generateText',
  'template-key',
  { systemPrompt: 'Generate text...' },
  { 
    model: 'gemini-1.5-flash',
    temperature: 0.7,
    max_tokens: 1000 
  }
);

// Force OpenAI usage
const openaiResult = await llmService.optimizedLLMCall(
  'generateText',
  'template-key',
  { systemPrompt: 'Generate text...' },
  { 
    model: 'gpt-4o-mini',
    temperature: 0.7,
    max_tokens: 1000 
  }
);
```

## Configuration Options

### Provider Priority

Set your preferred default provider:

```bash
# Use Gemini as default
LLM_DEFAULT_PROVIDER=gemini

# Use OpenAI as default
LLM_DEFAULT_PROVIDER=openai
```

### Model-Specific Configuration

Different models have different capabilities and costs:

| Model | Provider | Speed | Quality | Cost | Best For |
|-------|----------|-------|---------|------|----------|
| gemini-1.5-flash | Gemini | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Quick tasks, high volume |
| gemini-1.5-pro | Gemini | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | Complex reasoning |
| gpt-4o-mini | OpenAI | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | Balanced performance |
| gpt-4o | OpenAI | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | Highest quality |

## Error Handling

The system includes automatic fallback mechanisms:

1. **Provider Fallback**: If Gemini is unavailable, falls back to OpenAI
2. **Error Recovery**: Graceful handling of API errors
3. **Configuration Validation**: Validates API keys on startup

## Monitoring

### Token Usage

Both providers are monitored for token usage:

```typescript
// Token usage is automatically tracked
tokenMonitor.trackUsage({
  endpoint: 'generateText',
  operation: 'vocabulary',
  inputTokens: 150,
  outputTokens: 75,
  model: 'gemini-1.5-flash',
  userId: 'user123',
});
```

### Performance Metrics

Model performance is tracked and used for automatic selection:

- **Latency**: Response time
- **Quality**: Success rate
- **Cost**: Token costs
- **Reliability**: Error rates

## Testing

### Unit Tests

Run Gemini-specific tests:

```bash
yarn test src/backend/services/gemini.service.test.ts
```

### Integration Tests

Run provider integration tests:

```bash
yarn test src/backend/services/llm-provider-integration.test.ts
```

## Troubleshooting

### Common Issues

1. **API Key Not Working**
   - Verify the API key is correct
   - Check if the key has proper permissions
   - Ensure billing is enabled for your Google Cloud project

2. **Model Not Found**
   - Verify the model name is correct
   - Check if the model is available in your region

3. **Rate Limiting**
   - Implement proper retry logic
   - Consider using different models for different use cases

### Debug Mode

Enable debug logging:

```bash
LLM_LOG_LEVEL=debug
```

## Best Practices

1. **Cost Optimization**
   - Use `gemini-1.5-flash` for high-volume, simple tasks
   - Use `gemini-1.5-pro` for complex reasoning
   - Monitor token usage regularly

2. **Performance**
   - Cache frequently used results
   - Use batch processing when possible
   - Choose models based on latency requirements

3. **Reliability**
   - Always configure both providers for fallback
   - Implement proper error handling
   - Monitor API quotas and limits

## Migration Guide

### From OpenAI Only

1. Add Gemini environment variables
2. Set `LLM_DEFAULT_PROVIDER=gemini` if desired
3. Test with a subset of traffic
4. Monitor performance and costs
5. Gradually increase Gemini usage

### Configuration Examples

#### Development Environment
```bash
LLM_DEFAULT_PROVIDER=gemini
LLM_GEMINI_MODEL=gemini-1.5-flash
LLM_OPENAI_MODEL=gpt-4o-mini
```

#### Production Environment
```bash
LLM_DEFAULT_PROVIDER=openai
LLM_GEMINI_MODEL=gemini-1.5-pro
LLM_OPENAI_MODEL=gpt-4o
```

## Support

For issues related to Gemini integration:

1. Check the logs for error messages
2. Verify API key configuration
3. Test with simple requests first
4. Consult Google AI documentation for model-specific issues

## Future Enhancements

Planned improvements:

- [ ] Automatic model selection based on task complexity
- [ ] Cost optimization algorithms
- [ ] Advanced caching strategies
- [ ] Multi-region support
- [ ] Custom model fine-tuning support
